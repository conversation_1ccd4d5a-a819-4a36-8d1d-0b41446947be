<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';

defineOptions({
  name: 'DashboardContain'
});

interface Props {
  /** 设计宽度 */
  designWidth?: number;
  /** 设计高度 */
  designHeight?: number;
  /** 是否启用缩放 */
  enableScale?: boolean;
}

// 组件属性默认值
const props = withDefaults(defineProps<Props>(), {
  designWidth: 1920,
  designHeight: 1080,
  enableScale: true
});

// 大屏容器引用
const containerRef = ref<HTMLElement | null>(null);

/**
 * 计算大屏缩放比例
 * @returns {number} 缩放比例
 */
const getScale = () => {
  const ww = window.innerWidth / props.designWidth;
  const wh = window.innerHeight / props.designHeight;
  // 限制最小缩放比例，确保在小屏幕上不会过度缩小
  return ww < wh ? ww : wh;
};

// 防抖定时器和动画帧ID
const resizeTimer: number | null = null;
let animationFrameId: number | null = null;

/**
 * 响应窗口大小变化，重新计算缩放比例
 */
const resize = () => {
  if (!containerRef.value) return;

  // 取消之前的动画帧和定时器
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  // 使用 requestAnimationFrame 确保在下一帧执行
  animationFrameId = requestAnimationFrame(() => {
    if (!containerRef.value) return;

    const element = containerRef.value;

    if (props.enableScale) {
      // 缩放模式：固定尺寸，居中缩放
      const scale = getScale();
      element.style.cssText = `
        transform: scale(${scale}) translate(-50%, -50%);
        transform-origin: top left;
        overflow: hidden;
        width: ${props.designWidth}px;
        height: ${props.designHeight}px;
        position: fixed;
        left: 50%;
        top: 50%;
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      `;
    } else {
      // 非缩放模式：全屏显示，主容器不滚动
      element.style.cssText = `
        transform: none;
        transform-origin: top left;
        overflow: hidden;
        width: 100%;
        height: 100%;
        position: relative;
        left: 0;
        top: 0;
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      `;
    }
  });
};

/**
 * 初始化大屏容器尺寸和缩放
 */
onMounted(() => {
  // 初始化时直接调用 resize 方法
  resize();
  window.addEventListener('resize', resize);
});

/**
 * 监听enableScale属性变化
 */
watch(
  () => props.enableScale,
  () => {
    resize();
  }
);

/**
 * 组件销毁前移除事件监听
 */
onBeforeUnmount(() => {
  window.removeEventListener('resize', resize);
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
});
</script>

<template>
  <div class="h-full w-full overflow-hidden bg-[#082761]">
    <!-- 主容器 -->
    <div
      ref="containerRef"
      class="z-999 flex flex-col origin-top-left transform-gpu transition-all duration-600 ease-out will-change-transform"
    >
      <slot />
    </div>
  </div>
</template>
