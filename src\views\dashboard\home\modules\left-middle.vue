<script setup lang="ts">
import { computed, ref } from 'vue';
import { NButton, NIcon } from 'naive-ui';
import DashboardTable from '@/components/dashboard/common/dashboard-table.vue';

defineOptions({
  name: 'LeftMiddleContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});

// 数据类型定义
interface DepartmentData {
  id: number;
  department: string;
  received: number;
  resolved: number;
  resolveRate: number;
  timeout: number;
  lowSatisfaction: number;
}

// 当前选择的时间类型
const timeType = ref<'year' | 'month'>('month');

// 模拟数据
const mockData = {
  year: [
    {
      id: 1,
      department: '技术部',
      received: 1200,
      resolved: 1150,
      resolveRate: 95.8,
      timeout: 25,
      lowSatisfaction: 15
    },
    { id: 2, department: '销售部', received: 980, resolved: 920, resolveRate: 93.9, timeout: 35, lowSatisfaction: 22 },
    {
      id: 3,
      department: '客服部',
      received: 1500,
      resolved: 1420,
      resolveRate: 94.7,
      timeout: 45,
      lowSatisfaction: 28
    },
    { id: 4, department: '财务部', received: 650, resolved: 630, resolveRate: 96.9, timeout: 12, lowSatisfaction: 8 },
    { id: 5, department: '人事部', received: 420, resolved: 400, resolveRate: 95.2, timeout: 15, lowSatisfaction: 10 },
    { id: 6, department: '运营部', received: 850, resolved: 800, resolveRate: 94.1, timeout: 30, lowSatisfaction: 18 },
    { id: 7, department: '市场部', received: 720, resolved: 680, resolveRate: 94.4, timeout: 28, lowSatisfaction: 16 },
    { id: 8, department: '产品部', received: 560, resolved: 540, resolveRate: 96.4, timeout: 18, lowSatisfaction: 12 }
  ],
  month: [
    { id: 1, department: '技术部', received: 120, resolved: 115, resolveRate: 95.8, timeout: 3, lowSatisfaction: 2 },
    { id: 2, department: '销售部', received: 98, resolved: 92, resolveRate: 93.9, timeout: 4, lowSatisfaction: 3 },
    { id: 3, department: '客服部', received: 150, resolved: 142, resolveRate: 94.7, timeout: 5, lowSatisfaction: 3 },
    { id: 4, department: '财务部', received: 65, resolved: 63, resolveRate: 96.9, timeout: 1, lowSatisfaction: 1 },
    { id: 5, department: '人事部', received: 42, resolved: 40, resolveRate: 95.2, timeout: 2, lowSatisfaction: 1 },
    { id: 6, department: '运营部', received: 85, resolved: 80, resolveRate: 94.1, timeout: 3, lowSatisfaction: 2 },
    { id: 7, department: '市场部', received: 72, resolved: 68, resolveRate: 94.4, timeout: 3, lowSatisfaction: 2 },
    { id: 8, department: '产品部', received: 56, resolved: 54, resolveRate: 96.4, timeout: 2, lowSatisfaction: 1 }
  ]
};

// 当前数据
const currentData = computed(() => mockData[timeType.value]);

// 合计数据
const totalData = computed(() => {
  const data = currentData.value;
  const total = {
    department: '合计',
    received: data.reduce((sum, item) => sum + item.received, 0),
    resolved: data.reduce((sum, item) => sum + item.resolved, 0),
    timeout: data.reduce((sum, item) => sum + item.timeout, 0),
    lowSatisfaction: data.reduce((sum, item) => sum + item.lowSatisfaction, 0)
  };

  return {
    ...total,
    resolveRate: total.received > 0 ? Number(((total.resolved / total.received) * 100).toFixed(1)) : 0
  };
});

// 切换时间类型
const toggleTimeType = () => {
  timeType.value = timeType.value === 'year' ? 'month' : 'year';
};

// 表格列定义
const columns = [
  { title: '部门', key: 'department', align: 'center' as const },
  { title: '受理量', key: 'received', align: 'center' as const, cellClass: 'text-blue-300' },
  { title: '解决量', key: 'resolved', align: 'center' as const, cellClass: 'text-green-400' },
  {
    title: '解决率',
    key: 'resolveRate',
    align: 'center' as const,
    cellClass: 'text-yellow-400',
    render: (row: DepartmentData) => `${row.resolveRate}%`
  },
  { title: '超时量', key: 'timeout', align: 'center' as const, cellClass: 'text-red-400' },
  { title: '低满量', key: 'lowSatisfaction', align: 'center' as const, cellClass: 'text-orange-400' }
];
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <template #actions>
      <NButton size="small" type="primary" ghost class="text-12px" @click="toggleTimeType">
        <SvgIcon icon="mdi:calendar-clock" />
        {{ timeType === 'year' ? '年度' : '月度' }}
      </NButton>
    </template>

    <!-- 主要内容区域 -->
    <div class="absolute inset-0 flex flex-col">
      <!-- 表格容器 -->
      <div class="min-h-0 flex-1 overflow-hidden">
        <DashboardTable
          :columns="columns"
          :data="currentData"
          :total-data="totalData"
          :show-header="true"
          :show-total="true"
          :auto-scroll="true"
          :scroll-speed="50"
          height="100%"
        />
      </div>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full p-24px">
        <!-- 全屏标题和切换按钮 -->
        <div class="mb-24px flex items-center justify-between">
          <h2 class="text-24px text-white font-600">
            {{ props.title }} - {{ timeType === 'year' ? '年度' : '月度' }}详细数据
          </h2>
          <NButton type="primary" @click="toggleTimeType">
            <template #icon>
              <NIcon>
                <SvgIcon icon="mdi:calendar-clock" />
              </NIcon>
            </template>
            切换到{{ timeType === 'year' ? '月度' : '年度' }}
          </NButton>
        </div>

        <!-- 全屏表格 -->
        <DashboardTable
          :columns="columns"
          :data="currentData"
          :total-data="totalData"
          :show-header="true"
          :show-total="true"
          :auto-scroll="false"
          height="400px"
        />

        <!-- 全屏合计信息 -->
        <div class="grid grid-cols-3 mt-24px gap-16px">
          <div class="border border-blue-500/30 rounded-8px bg-blue-900/30 p-16px text-center">
            <div class="mb-4px text-12px text-white/60">总受理量</div>
            <div class="text-20px text-blue-300 font-600">{{ totalData.received }}</div>
          </div>
          <div class="border border-green-500/30 rounded-8px bg-green-900/30 p-16px text-center">
            <div class="mb-4px text-12px text-white/60">总解决量</div>
            <div class="text-20px text-green-400 font-600">{{ totalData.resolved }}</div>
          </div>
          <div class="border border-yellow-500/30 rounded-8px bg-yellow-900/30 p-16px text-center">
            <div class="mb-4px text-12px text-white/60">整体解决率</div>
            <div class="text-20px text-yellow-400 font-600">{{ totalData.resolveRate }}%</div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
