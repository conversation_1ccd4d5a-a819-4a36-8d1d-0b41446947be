<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useEcharts } from '@/hooks/common/echarts';
import type { ECOption } from '@/hooks/common/echarts';
import csMapData from '@/assets/dashboard/maps/cs.json';

defineOptions({
  name: 'CenterTopContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});

// 模拟网络指标数据
const networkData = ref([
  { name: '芙蓉区', value: 95, status: 'excellent', lat: 28.193106, lng: 112.988094 },
  { name: '天心区', value: 88, status: 'good', lat: 28.112444, lng: 112.989888 },
  { name: '岳麓区', value: 92, status: 'excellent', lat: 28.235194, lng: 112.931944 },
  { name: '开福区', value: 85, status: 'good', lat: 28.256944, lng: 112.986111 },
  { name: '雨花区', value: 78, status: 'warning', lat: 28.135833, lng: 113.038056 },
  { name: '望城区', value: 82, status: 'good', lat: 28.347778, lng: 112.819167 },
  { name: '长沙县', value: 75, status: 'warning', lat: 28.237778, lng: 113.080556 },
  { name: '浏阳市', value: 70, status: 'error', lat: 28.163889, lng: 113.642778 },
  { name: '宁乡市', value: 73, status: 'warning', lat: 28.253889, lng: 112.553611 }
]);

// 状态配置
const statusConfig = {
  excellent: { color: '#52c41a', label: '优秀' },
  good: { color: '#1890ff', label: '良好' },
  warning: { color: '#faad14', label: '警告' },
  error: { color: '#ff4d4f', label: '异常' }
};

// 地图配置
const { domRef, updateOptions } = useEcharts(() => {
  const option: ECOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#1890ff',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: (params: any) => {
        if (params.componentType === 'geo') {
          const data = networkData.value.find(item => item.name === params.name);
          if (data) {
            const statusInfo = statusConfig[data.status as keyof typeof statusConfig];
            return `
              <div style="padding: 8px;">
                <div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>
                <div style="color: ${statusInfo.color};">网络质量: ${data.value}%</div>
                <div style="color: ${statusInfo.color};">状态: ${statusInfo.label}</div>
              </div>
            `;
          }
        }
        return params.name;
      }
    },
    geo: {
      map: 'changsha',
      roam: true,
      zoom: 1.2,
      center: [112.98, 28.2],
      itemStyle: {
        areaColor: 'rgba(24, 144, 255, 0.1)',
        borderColor: 'rgba(24, 144, 255, 0.6)',
        borderWidth: 1
      },
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(24, 144, 255, 0.3)',
          borderColor: '#1890ff',
          borderWidth: 2
        }
      },
      label: {
        show: true,
        color: '#ffffff',
        fontSize: 10,
        fontWeight: 'bold'
      }
    },
    series: [
      {
        type: 'scatter',
        coordinateSystem: 'geo',
        data: networkData.value.map(item => ({
          name: item.name,
          value: [item.lng, item.lat, item.value],
          itemStyle: {
            color: statusConfig[item.status as keyof typeof statusConfig].color
          }
        })),
        symbolSize: (val: number[]) => Math.max(8, val[2] / 5),
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(24, 144, 255, 0.8)'
        },
        emphasis: {
          scale: true,
          itemStyle: {
            shadowBlur: 20
          }
        }
      },
      {
        type: 'effectScatter',
        coordinateSystem: 'geo',
        data: networkData.value
          .filter(item => item.status === 'error' || item.status === 'warning')
          .map(item => ({
            name: item.name,
            value: [item.lng, item.lat, item.value],
            itemStyle: {
              color: statusConfig[item.status as keyof typeof statusConfig].color
            }
          })),
        symbolSize: 12,
        showEffectOn: 'render',
        rippleEffect: {
          brushType: 'stroke',
          scale: 3,
          period: 2
        },
        itemStyle: {
          shadowBlur: 10
        }
      }
    ]
  };
  return option;
});

// 获取状态
const getStatus = (value: number) => {
  if (value >= 90) return 'excellent';
  if (value >= 80) return 'good';
  if (value >= 70) return 'warning';
  return 'error';
};

// 注册地图
onMounted(() => {
  // 注册长沙地图数据
  import('echarts').then(echarts => {
    echarts.registerMap('changsha', csMapData as any);
  });

  // 模拟数据更新
  setInterval(() => {
    networkData.value = networkData.value.map(item => {
      const newValue = Math.max(60, Math.min(100, item.value + (Math.random() - 0.5) * 10));
      return {
        ...item,
        value: newValue,
        status: getStatus(newValue)
      };
    });

    updateOptions(opts => {
      if (opts.series && Array.isArray(opts.series) && opts.series.length >= 2) {
        const scatterSeries = opts.series[0] as any;
        const effectScatterSeries = opts.series[1] as any;

        scatterSeries.data = networkData.value.map(item => ({
          name: item.name,
          value: [item.lng, item.lat, item.value],
          itemStyle: {
            color: statusConfig[item.status as keyof typeof statusConfig].color
          }
        }));

        effectScatterSeries.data = networkData.value
          .filter(item => item.status === 'error' || item.status === 'warning')
          .map(item => ({
            name: item.name,
            value: [item.lng, item.lat, item.value],
            itemStyle: {
              color: statusConfig[item.status as keyof typeof statusConfig].color
            }
          }));
      }

      return opts;
    });
  }, 5000);
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <!-- 地图容器 -->
    <div class="h-full w-full flex flex-col">
      <!-- 状态指示器 -->
      <div class="mb-12px flex items-center justify-between">
        <div class="flex items-center gap-16px text-sm">
          <div class="flex items-center gap-6px">
            <div class="h-8px w-8px rounded-full" :style="{ backgroundColor: statusConfig.excellent.color }" />
            <span class="text-white/80">优秀 (≥90%)</span>
          </div>
          <div class="flex items-center gap-6px">
            <div class="h-8px w-8px rounded-full" :style="{ backgroundColor: statusConfig.good.color }" />
            <span class="text-white/80">良好 (80-89%)</span>
          </div>
          <div class="flex items-center gap-6px">
            <div class="h-8px w-8px rounded-full" :style="{ backgroundColor: statusConfig.warning.color }" />
            <span class="text-white/80">警告 (70-79%)</span>
          </div>
          <div class="flex items-center gap-6px">
            <div class="h-8px w-8px rounded-full" :style="{ backgroundColor: statusConfig.error.color }" />
            <span class="text-white/80">异常 (&lt;70%)</span>
          </div>
        </div>

        <!-- 实时状态 -->
        <div class="flex items-center gap-8px text-sm text-white/60">
          <div class="h-6px w-6px animate-pulse rounded-full bg-green-400" />
          <span>实时监控</span>
        </div>
      </div>

      <!-- 地图区域 -->
      <div ref="domRef" class="min-h-0 flex-1" />
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full w-full p-24px">
        <!-- 全屏状态指示器 -->
        <div class="mb-24px flex items-center justify-between">
          <div class="flex items-center gap-24px">
            <div class="flex items-center gap-8px">
              <div class="h-12px w-12px rounded-full" :style="{ backgroundColor: statusConfig.excellent.color }" />
              <span class="text-lg text-white/90">优秀 (≥90%)</span>
            </div>
            <div class="flex items-center gap-8px">
              <div class="h-12px w-12px rounded-full" :style="{ backgroundColor: statusConfig.good.color }" />
              <span class="text-lg text-white/90">良好 (80-89%)</span>
            </div>
            <div class="flex items-center gap-8px">
              <div class="h-12px w-12px rounded-full" :style="{ backgroundColor: statusConfig.warning.color }" />
              <span class="text-lg text-white/90">警告 (70-79%)</span>
            </div>
            <div class="flex items-center gap-8px">
              <div class="h-12px w-12px rounded-full" :style="{ backgroundColor: statusConfig.error.color }" />
              <span class="text-lg text-white/90">异常 (<70%)</span>
            </div>
          </div>

          <!-- 全屏实时状态 -->
          <div class="flex items-center gap-12px text-lg text-white/70">
            <div class="h-8px w-8px animate-pulse rounded-full bg-green-400" />
            <span>实时监控中...</span>
          </div>
        </div>

        <!-- 全屏地图 -->
        <div class="h-[calc(100%-80px)] w-full">
          <div ref="domRef" class="h-full w-full" />
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
