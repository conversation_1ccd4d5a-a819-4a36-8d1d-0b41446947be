<script setup lang="ts">
defineOptions({
  name: 'LeftBottomContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <div class="h-full flex items-center justify-center">
      <slot>
        <div class="text-center">
          <div class="mb-16px text-4xl text-purple-400">🔍</div>
          <div class="text-white/80">数据洞察</div>
          <div class="mt-8px text-sm text-white/50">左下容器内容</div>
        </div>
      </slot>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full flex items-center justify-center">
        <div class="text-center">
          <div class="mb-24px text-6xl text-purple-400">🔍</div>
          <div class="text-2xl text-white/90">{{ props.title }} - 深度分析</div>
          <div class="mt-12px text-white/60">这里可以展示详细的数据洞察内容</div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
