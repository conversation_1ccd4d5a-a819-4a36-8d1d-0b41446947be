<script setup lang="ts">
defineOptions({
  name: 'RightTopContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'metric',
  animationDelay: 0
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <div class="h-full flex items-center justify-center">
      <slot>
        <div class="text-center">
          <div class="mb-16px text-4xl text-green-400">⚡</div>
          <div class="text-lg text-white/90 font-600">实时指标</div>
          <div class="mt-16px space-y-12px">
            <div class="flex items-center justify-between">
              <span class="text-sm text-white/60">CPU使用率</span>
              <span class="text-green-400 font-600">45%</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-white/60">内存使用</span>
              <span class="text-yellow-400 font-600">68%</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-white/60">网络流量</span>
              <span class="text-blue-400 font-600">1.2GB</span>
            </div>
          </div>
        </div>
      </slot>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full flex items-center justify-center">
        <div class="max-w-2xl text-center">
          <div class="mb-24px text-6xl text-green-400">⚡</div>
          <div class="mb-32px text-2xl text-white/90">{{ props.title }} - 系统监控</div>

          <div class="grid grid-cols-2 gap-24px">
            <div class="space-y-16px">
              <div class="text-left">
                <div class="mb-8px text-sm text-white/60">CPU使用率</div>
                <div class="text-2xl text-green-400 font-600">45%</div>
                <div class="mt-8px h-2 w-full rounded-full bg-gray-700">
                  <div class="h-2 rounded-full bg-green-400" style="width: 45%"></div>
                </div>
              </div>
              <div class="text-left">
                <div class="mb-8px text-sm text-white/60">内存使用</div>
                <div class="text-2xl text-yellow-400 font-600">68%</div>
                <div class="mt-8px h-2 w-full rounded-full bg-gray-700">
                  <div class="h-2 rounded-full bg-yellow-400" style="width: 68%"></div>
                </div>
              </div>
            </div>
            <div class="space-y-16px">
              <div class="text-left">
                <div class="mb-8px text-sm text-white/60">磁盘使用</div>
                <div class="text-2xl text-blue-400 font-600">32%</div>
                <div class="mt-8px h-2 w-full rounded-full bg-gray-700">
                  <div class="h-2 rounded-full bg-blue-400" style="width: 32%"></div>
                </div>
              </div>
              <div class="text-left">
                <div class="mb-8px text-sm text-white/60">网络流量</div>
                <div class="text-2xl text-purple-400 font-600">1.2GB</div>
                <div class="mt-4px text-sm text-white/50">实时传输速率: 125MB/s</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
