<script setup lang="ts">
import { ref, watch } from 'vue';
import { NForm, NFormItem, NIcon, NSwitch, NTooltip } from 'naive-ui';
import DashboardDialog from '@/components/dashboard/common/dashboard-dialog.vue';

defineOptions({
  name: 'DashboardSettings'
});

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'update:scale', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 设置状态
const scaleEnabled = ref(true);

// 监听缩放设置变化，自动保存
watch(scaleEnabled, newValue => {
  // 自动保存到本地存储
  localStorage.setItem('dashboard-scale-enabled', JSON.stringify(newValue));
  // 通知父组件更新
  emit('update:scale', newValue);
  // 触发自定义事件，通知其他组件
  window.dispatchEvent(
    new CustomEvent('dashboard-scale-change', {
      detail: { scaleEnabled: newValue }
    })
  );
});

// 初始化设置
const initSettings = () => {
  const savedScale = localStorage.getItem('dashboard-scale-enabled');
  if (savedScale) {
    scaleEnabled.value = JSON.parse(savedScale);
    emit('update:scale', scaleEnabled.value);
  }
};

// 组件挂载时初始化设置
initSettings();
</script>

<template>
  <DashboardDialog
    :visible="props.visible"
    title="大屏设置"
    title-icon="mdi:cog"
    width="min(90vw, 500px)"
    :show-footer="false"
    :full-content="false"
    @update:visible="emit('update:visible', $event)"
  >
    <div class="flex justify-center">
      <div class="max-w-400px w-full">
        <NForm label-placement="left" label-width="auto" class="w-full">
          <!-- 缩放控制 -->
          <NFormItem class="flex items-center justify-between">
            <template #label>
              <div class="flex items-center gap-8px">
                <NIcon size="16" class="text-blue-400">
                  <SvgIcon icon="mdi:resize" />
                </NIcon>
                <span class="text-16px text-white font-500">自适应缩放</span>
                <NTooltip trigger="hover" placement="top">
                  <template #trigger>
                    <NIcon size="14" class="ml-6px cursor-help text-blue-400">
                      <SvgIcon icon="mdi:information-outline" />
                    </NIcon>
                  </template>
                  <div class="max-w-280px">
                    <p class="text-13px leading-relaxed">
                      自适应缩放功能可以根据屏幕尺寸自动调整大屏内容的显示比例，确保在不同分辨率下都能获得最佳的视觉效果。关闭此功能后，大屏将以原始尺寸显示。
                    </p>
                  </div>
                </NTooltip>
              </div>
            </template>

            <div class="flex items-center gap-12px">
              <span class="text-14px text-gray-400">
                {{ scaleEnabled ? '已启用' : '已禁用' }}
              </span>
              <NSwitch v-model:value="scaleEnabled" size="medium" class="scale-switch" />
            </div>
          </NFormItem>

          <!-- 预留其他功能设置区域 -->
          <!-- 可以在这里添加更多设置项 -->
        </NForm>
      </div>
    </div>
  </DashboardDialog>
</template>
