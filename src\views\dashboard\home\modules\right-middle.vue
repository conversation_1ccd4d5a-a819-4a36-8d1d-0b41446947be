<script setup lang="ts">
defineOptions({
  name: 'RightMiddleContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <div class="h-full flex items-center justify-center">
      <slot>
        <div class="w-full text-center">
          <div class="mb-16px text-4xl text-purple-400">📊</div>
          <div class="mb-16px text-lg text-white/90 font-600">数据分析</div>

          <!-- 环形进度图 -->
          <div class="relative mx-auto mb-16px h-32 w-32">
            <svg class="h-32 w-32 transform -rotate-90" viewBox="0 0 36 36">
              <path
                class="text-gray-700"
                stroke="currentColor"
                stroke-width="3"
                fill="none"
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              />
              <path
                class="text-purple-400"
                stroke="currentColor"
                stroke-width="3"
                stroke-linecap="round"
                fill="none"
                stroke-dasharray="75, 100"
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              />
            </svg>
            <div class="absolute inset-0 flex items-center justify-center">
              <span class="text-lg text-purple-400 font-600">75%</span>
            </div>
          </div>

          <div class="text-sm text-white/60">完成度</div>
        </div>
      </slot>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full flex items-center justify-center">
        <div class="text-center">
          <div class="mb-24px text-6xl text-purple-400">📊</div>
          <div class="mb-32px text-2xl text-white/90">{{ props.title }} - 详细分析</div>

          <div class="grid grid-cols-3 gap-32px">
            <!-- 环形图1 -->
            <div class="text-center">
              <div class="relative mx-auto mb-16px h-40 w-40">
                <svg class="h-40 w-40 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    class="text-gray-700"
                    stroke="currentColor"
                    stroke-width="2"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    class="text-purple-400"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    fill="none"
                    stroke-dasharray="75, 100"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-xl text-purple-400 font-600">75%</span>
                </div>
              </div>
              <div class="text-white/80">项目完成度</div>
            </div>

            <!-- 环形图2 -->
            <div class="text-center">
              <div class="relative mx-auto mb-16px h-40 w-40">
                <svg class="h-40 w-40 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    class="text-gray-700"
                    stroke="currentColor"
                    stroke-width="2"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    class="text-green-400"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    fill="none"
                    stroke-dasharray="85, 100"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-xl text-green-400 font-600">85%</span>
                </div>
              </div>
              <div class="text-white/80">用户满意度</div>
            </div>

            <!-- 环形图3 -->
            <div class="text-center">
              <div class="relative mx-auto mb-16px h-40 w-40">
                <svg class="h-40 w-40 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    class="text-gray-700"
                    stroke="currentColor"
                    stroke-width="2"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    class="text-blue-400"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    fill="none"
                    stroke-dasharray="92, 100"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-xl text-blue-400 font-600">92%</span>
                </div>
              </div>
              <div class="text-white/80">系统稳定性</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
