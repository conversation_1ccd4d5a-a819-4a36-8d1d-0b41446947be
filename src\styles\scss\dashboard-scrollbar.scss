// 大屏滚动条样式 - 悬停显示，不占用容器宽度
.dashboard-scrollbar {
  /* Firefox - 使用 overlay 样式，不占用空间 */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent; // 默认透明
  scrollbar-gutter: stable; // 保持布局稳定

  /* Webkit - overlay 样式滚动条 */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    // 使用 overlay 样式，滚动条浮动在内容上方
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: transparent; // 默认透明
    border-radius: 4px;
    // 不占用布局空间
    margin: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent; // 默认透明
    border-radius: 4px;
    transition: all 0.3s ease;
    // 添加边距，避免贴边显示
    border: 2px solid transparent;
    background-clip: content-box;
  }

  // 容器悬停时显示滚动条
  &:hover {
    scrollbar-color: rgba(24, 144, 255, 0.6) rgba(255, 255, 255, 0.1);

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(24, 144, 255, 0.6);
      border: 1px solid rgba(24, 144, 255, 0.3);
      box-shadow: 0 0 4px rgba(24, 144, 255, 0.2);
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(24, 144, 255, 0.8);
      box-shadow: 0 0 8px rgba(24, 144, 255, 0.4);
    }

    &::-webkit-scrollbar-thumb:active {
      background: rgba(24, 144, 255, 1);
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

// 应用到大屏容器
.dashboard-container {
  @extend .dashboard-scrollbar;
}

// 应用到卡片内容区域
.dashboard-card .card-content {
  @extend .dashboard-scrollbar;
}

// 全局大屏滚动条样式（当关闭缩放时）
.dashboard-layout {
  .dashboard-scrollbar,
  &.no-scale {
    @extend .dashboard-scrollbar;
  }
}

// 细滚动条变体（用于小区域）- 悬停显示，不占用空间
.dashboard-scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent; // 默认透明
  scrollbar-gutter: stable; // 保持布局稳定

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: transparent; // 默认透明
    border-radius: 3px;
    margin: 1px;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent; // 默认透明
    border-radius: 3px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    background-clip: content-box;
  }

  // 容器悬停时显示滚动条
  &:hover {
    scrollbar-color: rgba(24, 144, 255, 0.5) rgba(255, 255, 255, 0.05);

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.03);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(24, 144, 255, 0.5);
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(24, 144, 255, 0.7);
    }
  }
}
