<script setup lang="ts">
defineOptions({
  name: 'CenterBottomContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'info',
  animationDelay: 0
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
  >
    <div class="h-full flex items-center justify-center">
      <slot>
        <div class="w-full">
          <div class="mb-20px text-center">
            <div class="mb-8px text-4xl text-orange-400">📋</div>
            <div class="text-lg text-white/90">数据表格</div>
          </div>

          <!-- 模拟表格数据 - 添加更多数据测试滚动 -->
          <div class="space-y-8px">
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">销售额</span>
              <span class="text-orange-400 font-600">¥123,456</span>
            </div>
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">订单数</span>
              <span class="text-green-400 font-600">1,234</span>
            </div>
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">用户数</span>
              <span class="text-blue-400 font-600">5,678</span>
            </div>
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">转化率</span>
              <span class="text-purple-400 font-600">12.5%</span>
            </div>
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">客单价</span>
              <span class="text-cyan-400 font-600">¥89.5</span>
            </div>
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">复购率</span>
              <span class="text-pink-400 font-600">35.2%</span>
            </div>
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">活跃度</span>
              <span class="text-yellow-400 font-600">78.9%</span>
            </div>
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">留存率</span>
              <span class="text-indigo-400 font-600">65.4%</span>
            </div>
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">流失率</span>
              <span class="text-red-400 font-600">8.7%</span>
            </div>
            <div class="flex items-center justify-between rounded-6px bg-white/5 px-12px py-8px">
              <span class="text-white/80">增长率</span>
              <span class="text-emerald-400 font-600">+15.3%</span>
            </div>
          </div>
        </div>
      </slot>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full p-24px">
        <div class="mb-32px text-center">
          <div class="mb-16px text-6xl text-orange-400">📋</div>
          <div class="text-2xl text-white/90">{{ props.title }} - 详细数据表</div>
        </div>

        <!-- 全屏表格 -->
        <div class="grid grid-cols-2 gap-24px">
          <div class="space-y-12px">
            <h4 class="mb-16px text-lg text-white/80">核心指标</h4>
            <div class="flex items-center justify-between rounded-8px bg-white/5 px-16px py-12px">
              <span class="text-white/80">总销售额</span>
              <span class="text-lg text-orange-400 font-600">¥1,234,567</span>
            </div>
            <div class="flex items-center justify-between rounded-8px bg-white/5 px-16px py-12px">
              <span class="text-white/80">总订单数</span>
              <span class="text-lg text-green-400 font-600">12,345</span>
            </div>
            <div class="flex items-center justify-between rounded-8px bg-white/5 px-16px py-12px">
              <span class="text-white/80">活跃用户</span>
              <span class="text-lg text-blue-400 font-600">56,789</span>
            </div>
          </div>
          <div class="space-y-12px">
            <h4 class="mb-16px text-lg text-white/80">增长指标</h4>
            <div class="flex items-center justify-between rounded-8px bg-white/5 px-16px py-12px">
              <span class="text-white/80">月增长率</span>
              <span class="text-lg text-green-400 font-600">+15.2%</span>
            </div>
            <div class="flex items-center justify-between rounded-8px bg-white/5 px-16px py-12px">
              <span class="text-white/80">用户留存</span>
              <span class="text-lg text-purple-400 font-600">85.6%</span>
            </div>
            <div class="flex items-center justify-between rounded-8px bg-white/5 px-16px py-12px">
              <span class="text-white/80">转化率</span>
              <span class="text-lg text-yellow-400 font-600">12.8%</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
