/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./../components/common/app-provider.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    BooleanTag: typeof import('./../components/custom/boolean-tag.vue')['default']
    ButtonIcon: typeof import('./../components/custom/button-icon.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    DashboardCard: typeof import('./../components/dashboard/containers/dashboard-card.vue')['default']
    DashboardContainer: typeof import('./../components/dashboard/containers/DashboardContainer.vue')['default']
    DashboardDialog: typeof import('./../components/dashboard/common/dashboard-dialog.vue')['default']
    DashboardDialogExample: typeof import('./../components/dashboard/common/dashboard-dialog-example.vue')['default']
    DashboardEntrance: typeof import('./../components/dashboard/effects/dashboard-entrance.vue')['default']
    DashboardHeightConfig: typeof import('./../components/dashboard/containers/dashboard-height-config.vue')['default']
    DashboardLoading: typeof import('./../components/dashboard/effects/dashboard-loading.vue')['default']
    DashboardRefresh: typeof import('./../components/dashboard/effects/dashboard-refresh.vue')['default']
    DashboardTable: typeof import('./../components/dashboard/common/dashboard-table.vue')['default']
    DataTable: typeof import('./../components/common/data-table.vue')['default']
    DeptTree: typeof import('./../components/custom/dept-tree.vue')['default']
    DeptTreeSelect: typeof import('./../components/custom/dept-tree-select.vue')['default']
    DialogTest: typeof import('./../components/dashboard/common/dialog-test.vue')['default']
    DictRadio: typeof import('./../components/custom/dict-radio.vue')['default']
    DictSelect: typeof import('./../components/custom/dict-select.vue')['default']
    DictTag: typeof import('./../components/custom/dict-tag.vue')['default']
    EffectsDemo: typeof import('./../components/dashboard/effects/effects-demo.vue')['default']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FileUpload: typeof import('./../components/custom/file-upload.vue')['default']
    FormTip: typeof import('./../components/custom/form-tip.vue')['default']
    FullScreen: typeof import('./../components/common/full-screen.vue')['default']
    IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined')['default']
    IconEpCopyDocument: typeof import('~icons/ep/copy-document')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    'IconHugeicons:configuration01': typeof import('~icons/hugeicons/configuration01')['default']
    IconIcRoundRefresh: typeof import('~icons/ic/round-refresh')['default']
    IconIcRoundSearch: typeof import('~icons/ic/round-search')['default']
    IconLocalBanner: typeof import('~icons/local/banner')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    'IconMaterialSymbols:add': typeof import('~icons/material-symbols/add')['default']
    'IconMaterialSymbols:deleteOutline': typeof import('~icons/material-symbols/delete-outline')['default']
    'IconMaterialSymbols:downloadRounded': typeof import('~icons/material-symbols/download-rounded')['default']
    'IconMaterialSymbols:imageOutline': typeof import('~icons/material-symbols/image-outline')['default']
    'IconMaterialSymbols:refreshRounded': typeof import('~icons/material-symbols/refresh-rounded')['default']
    'IconMaterialSymbols:syncOutline': typeof import('~icons/material-symbols/sync-outline')['default']
    'IconMaterialSymbols:uploadRounded': typeof import('~icons/material-symbols/upload-rounded')['default']
    'IconMaterialSymbols:warningOutlineRounded': typeof import('~icons/material-symbols/warning-outline-rounded')['default']
    IconMaterialSymbolsAddRounded: typeof import('~icons/material-symbols/add-rounded')['default']
    IconMaterialSymbolsDeleteOutline: typeof import('~icons/material-symbols/delete-outline')['default']
    IconMaterialSymbolsDriveFileRenameOutlineOutline: typeof import('~icons/material-symbols/drive-file-rename-outline-outline')['default']
    'IconMdi:account': typeof import('~icons/mdi/account')['default']
    'IconMdi:cellphone': typeof import('~icons/mdi/cellphone')['default']
    'IconMdi:github': typeof import('~icons/mdi/github')['default']
    IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin')['default']
    IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin')['default']
    IconMdiDrag: typeof import('~icons/mdi/drag')['default']
    IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc')['default']
    IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return')['default']
    'IconQuill:collapse': typeof import('~icons/quill/collapse')['default']
    'IconQuill:expand': typeof import('~icons/quill/expand')['default']
    'IconSimpleIcons:gitee': typeof import('~icons/simple-icons/gitee')['default']
    IconUilSearch: typeof import('~icons/uil/search')['default']
    JsonPreview: typeof import('./../components/custom/json-preview.vue')['default']
    LangSwitch: typeof import('./../components/common/lang-switch.vue')['default']
    LookForward: typeof import('./../components/custom/look-forward.vue')['default']
    MenuToggler: typeof import('./../components/common/menu-toggler.vue')['default']
    MenuTree: typeof import('./../components/custom/menu-tree.vue')['default']
    MenuTreeSelect: typeof import('./../components/custom/menu-tree-select.vue')['default']
    MonacoEditor: typeof import('./../components/common/monaco-editor.vue')['default']
    NA: typeof import('naive-ui')['NA']
    NAlert: typeof import('naive-ui')['NAlert']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBadge: typeof import('naive-ui')['NBadge']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCode: typeof import('naive-ui')['NCode']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDescriptions: typeof import('naive-ui')['NDescriptions']
    NDescriptionsItem: typeof import('naive-ui')['NDescriptionsItem']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NIcon: typeof import('naive-ui')['NIcon']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputGroupLabel: typeof import('naive-ui')['NInputGroupLabel']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLayout: typeof import('naive-ui')['NLayout']
    NLayoutContent: typeof import('naive-ui')['NLayoutContent']
    NLayoutSider: typeof import('naive-ui')['NLayoutSider']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NP: typeof import('naive-ui')['NP']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioButton: typeof import('naive-ui')['NRadioButton']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSlider: typeof import('naive-ui')['NSlider']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NStatistic: typeof import('naive-ui')['NStatistic']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NThing: typeof import('naive-ui')['NThing']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NTreeSelect: typeof import('naive-ui')['NTreeSelect']
    NUpload: typeof import('naive-ui')['NUpload']
    NUploadDragger: typeof import('naive-ui')['NUploadDragger']
    NWatermark: typeof import('naive-ui')['NWatermark']
    OssUpload: typeof import('./../components/custom/oss-upload.vue')['default']
    PinToggler: typeof import('./../components/common/pin-toggler.vue')['default']
    PostSelect: typeof import('./../components/custom/post-select.vue')['default']
    ReloadButton: typeof import('./../components/common/reload-button.vue')['default']
    RoleSelect: typeof import('./../components/custom/role-select.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SoybeanAvatar: typeof import('./../components/custom/soybean-avatar.vue')['default']
    StatusSwitch: typeof import('./../components/custom/status-switch.vue')['default']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../components/advanced/table-header-operation.vue')['default']
    TableRowCheckAlert: typeof import('./../components/advanced/table-row-check-alert.vue')['default']
    TableSiderLayout: typeof import('./../components/advanced/table-sider-layout.vue')['default']
    TenantSelect: typeof import('./../components/custom/tenant-select.vue')['default']
    ThemeSchemaSwitch: typeof import('./../components/common/theme-schema-switch.vue')['default']
    TinymceEditor: typeof import('./../components/custom/tinymce-editor.vue')['default']
    UserSelect: typeof import('./../components/custom/user-select.vue')['default']
    WaveBg: typeof import('./../components/custom/wave-bg.vue')['default']
  }
}
