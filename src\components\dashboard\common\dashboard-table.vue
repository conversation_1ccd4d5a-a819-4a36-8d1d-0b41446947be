<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

defineOptions({
  name: 'DashboardTable'
});

interface Column {
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (row: any) => string;
  cellClass?: string;
}

interface Props {
  columns: Column[];
  data: any[];
  showHeader?: boolean;
  showTotal?: boolean;
  autoScroll?: boolean;
  scrollSpeed?: number;
  totalData?: any;
  height?: string;
  headerClass?: string;
  bodyClass?: string;
  totalClass?: string;
  rowClass?: string | ((row: any, index: number) => string);
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: true,
  showTotal: false,
  autoScroll: false,
  scrollSpeed: 50,
  totalData: () => ({}),
  height: '100%',
  headerClass: 'bg-blue-900/30 border border-blue-500/30',
  bodyClass: 'border-l border-r border-blue-500/30',
  totalClass: 'bg-blue-800/40 border border-blue-500/30',
  rowClass: 'bg-blue-900/10 hover:bg-blue-800/20 transition-colors'
});

// 滚动相关
const scrollContainer = ref<HTMLElement>();
const scrollTimer = ref<number>();

// 开始滚动
const startScroll = () => {
  if (!props.autoScroll || !scrollContainer.value) return;

  scrollTimer.value = window.setInterval(() => {
    if (!scrollContainer.value) return;

    const container = scrollContainer.value;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;

    if (scrollTop + clientHeight >= scrollHeight) {
      container.scrollTop = 0;
    } else {
      container.scrollTop += 1;
    }
  }, props.scrollSpeed);
};

// 停止滚动
const stopScroll = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value);
    scrollTimer.value = undefined;
  }
};

// 获取行样式类
const getRowClass = (row: any, index: number): string => {
  if (typeof props.rowClass === 'function') {
    return props.rowClass(row, index);
  }
  return props.rowClass || '';
};

// 渲染单元格内容
const renderCell = (column: Column, row: any): string => {
  if (column.render) {
    return column.render(row);
  }
  return String(row[column.key] || '');
};

// 计算列的网格样式
const gridCols = computed(() => {
  const colCount = props.columns.length;
  return `grid-cols-${colCount}`;
});

onMounted(() => {
  if (props.autoScroll) {
    startScroll();
  }
});

onUnmounted(() => {
  stopScroll();
});
</script>

<template>
  <div class="dashboard-table h-full min-h-0 w-full flex flex-col overflow-hidden" :style="{ height }">
    <!-- 表头 -->
    <div v-if="showHeader" class="rounded-t-6px" :class="[headerClass]">
      <div class="grid gap-1px py-8px text-12px text-white/80 font-500" :class="[gridCols]">
        <div
          v-for="column in columns"
          :key="column.key"
          class="px-8px"
          :class="[`text-${column.align || 'center'}`]"
          :style="{ width: column.width ? `${column.width}px` : 'auto' }"
        >
          {{ column.title }}
        </div>
      </div>
    </div>

    <!-- 表体 - 滚动区域 -->
    <div
      ref="scrollContainer"
      class="min-h-0 flex-1 overflow-hidden"
      :class="[bodyClass]"
      @mouseenter="stopScroll"
      @mouseleave="startScroll"
    >
      <div class="space-y-1px">
        <!-- 如果开启自动滚动，循环显示数据 -->
        <template v-if="autoScroll">
          <template v-for="cycle in 3" :key="`cycle-${cycle}`">
            <div
              v-for="(row, index) in data"
              :key="`${cycle}-${index}`"
              class="grid gap-1px py-6px text-12px text-white/90"
              :class="[gridCols, getRowClass(row, index)]"
            >
              <div
                v-for="column in columns"
                :key="column.key"
                class="px-8px"
                :class="[`text-${column.align || 'center'}`, column.cellClass || '']"
                :style="{ width: column.width ? `${column.width}px` : 'auto' }"
              >
                {{ renderCell(column, row) }}
              </div>
            </div>
          </template>
        </template>

        <!-- 普通显示模式 -->
        <template v-else>
          <div
            v-for="(row, index) in data"
            :key="index"
            class="grid gap-1px py-6px text-12px text-white/90"
            :class="[gridCols, getRowClass(row, index)]"
          >
            <div
              v-for="column in columns"
              :key="column.key"
              class="px-8px"
              :class="[`text-${column.align || 'center'}`, column.cellClass || '']"
              :style="{ width: column.width ? `${column.width}px` : 'auto' }"
            >
              {{ renderCell(column, row) }}
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 合计行 -->
    <div v-if="showTotal && totalData" class="rounded-b-6px" :class="[totalClass]">
      <div class="grid gap-1px py-8px text-12px text-white font-600" :class="[gridCols]">
        <div
          v-for="column in columns"
          :key="column.key"
          class="px-8px"
          :class="[`text-${column.align || 'center'}`]"
          :style="{ width: column.width ? `${column.width}px` : 'auto' }"
        >
          {{ renderCell(column, totalData) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-table {
  /* 自定义滚动条样式 */
  --scrollbar-width: 4px;
  --scrollbar-track: rgba(59, 130, 246, 0.1);
  --scrollbar-thumb: rgba(59, 130, 246, 0.3);
  --scrollbar-thumb-hover: rgba(59, 130, 246, 0.5);

  /* 确保不影响父容器高度 */
  contain: layout style;
  max-height: 100%;
}

.dashboard-table ::-webkit-scrollbar {
  width: var(--scrollbar-width);
}

.dashboard-table ::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 2px;
}

.dashboard-table ::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 2px;
}

.dashboard-table ::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* 网格列数样式 */
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
.grid-cols-9 {
  grid-template-columns: repeat(9, minmax(0, 1fr));
}
.grid-cols-10 {
  grid-template-columns: repeat(10, minmax(0, 1fr));
}
</style>
