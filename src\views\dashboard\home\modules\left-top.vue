<script setup lang="ts">
import { computed, ref } from 'vue';

defineOptions({
  name: 'LeftTopContainer'
});

interface Props {
  title: string;
  icon: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  animationDelay: 0
});

// 指标状态枚举
enum MetricStatus {
  CHALLENGE_ACHIEVED = 'challenge_achieved', // 达到挑战值
  BASELINE_ACHIEVED = 'baseline_achieved', // 达到基准值
  CHALLENGE_MISSED = 'challenge_missed', // 未达挑战值
  BASELINE_MISSED = 'baseline_missed', // 未达基准值
  NO_DATA = 'no_data' // 未取到值
}

// 指标数据接口
interface MetricItem {
  id: number;
  name: string;
  value: number | string;
  unit: string;
  lastUpdate: string;
  baselineValue: number; // 基准值
  challengeValue: number; // 挑战值
  status: MetricStatus;
  description?: string; // 指标描述
}

// 状态配置
const statusConfig = {
  [MetricStatus.CHALLENGE_ACHIEVED]: {
    color: '#52c41a',
    label: '达到挑战值',
    icon: '🎯',
    description: '表现优异，超越挑战目标'
  },
  [MetricStatus.BASELINE_ACHIEVED]: {
    color: '#1890ff',
    label: '达到基准值',
    icon: '✅',
    description: '达到预期目标'
  },
  [MetricStatus.CHALLENGE_MISSED]: {
    color: '#faad14',
    label: '未达挑战值',
    icon: '⚠️',
    description: '接近目标，需要努力'
  },
  [MetricStatus.BASELINE_MISSED]: {
    color: '#ff4d4f',
    label: '未达基准值',
    icon: '❌',
    description: '低于预期，需要关注'
  },
  [MetricStatus.NO_DATA]: {
    color: '#8c8c8c',
    label: '未取到值',
    icon: '❓',
    description: '数据获取异常'
  }
};

// 计算指标状态
const calculateStatus = (value: number | string, baseline: number, challenge: number): MetricStatus => {
  if (typeof value !== 'number' || Number.isNaN(value)) {
    return MetricStatus.NO_DATA;
  }

  if (value >= challenge) {
    return MetricStatus.CHALLENGE_ACHIEVED;
  } else if (value >= baseline) {
    return MetricStatus.BASELINE_ACHIEVED;
  } else if (value >= baseline * 0.8) {
    // 80%基准值以上但未达基准值
    return MetricStatus.CHALLENGE_MISSED;
  }
  return MetricStatus.BASELINE_MISSED;
};

// 模拟指标数据 - 6个指标用于3x2布局
const metrics = ref<MetricItem[]>([
  {
    id: 1,
    name: '销售完成率',
    value: 125.6,
    unit: '%',
    lastUpdate: '2024-01-15 14:30',
    baselineValue: 100,
    challengeValue: 120,
    status: MetricStatus.CHALLENGE_ACHIEVED,
    description: '本月销售目标完成情况，包含所有产品线的综合数据'
  },
  {
    id: 2,
    name: '客户满意度',
    value: 98.5,
    unit: '%',
    lastUpdate: '2024-01-15 14:25',
    baselineValue: 95,
    challengeValue: 98,
    status: MetricStatus.CHALLENGE_ACHIEVED,
    description: '基于客户反馈调研的综合满意度评分'
  },
  {
    id: 3,
    name: '订单转化率',
    value: 8.2,
    unit: '%',
    lastUpdate: '2024-01-15 14:20',
    baselineValue: 10,
    challengeValue: 12,
    status: MetricStatus.BASELINE_MISSED,
    description: '从访问到下单的转化率，反映营销效果'
  },
  {
    id: 4,
    name: '库存周转率',
    value: 3.8,
    unit: '次',
    lastUpdate: '2024-01-15 14:15',
    baselineValue: 4,
    challengeValue: 5,
    status: MetricStatus.CHALLENGE_MISSED,
    description: '库存管理效率指标，反映资金使用效率'
  },
  {
    id: 5,
    name: '用户活跃度',
    value: 89.3,
    unit: '%',
    lastUpdate: '2024-01-15 14:10',
    baselineValue: 85,
    challengeValue: 90,
    status: MetricStatus.BASELINE_ACHIEVED,
    description: '日活跃用户占总用户的比例'
  },
  {
    id: 6,
    name: '成本控制率',
    value: 'N/A',
    unit: '%',
    lastUpdate: '2024-01-15 14:05',
    baselineValue: 90,
    challengeValue: 95,
    status: MetricStatus.NO_DATA,
    description: '运营成本控制效果，数据暂时无法获取'
  }
]);

// 重新计算所有指标的状态
metrics.value.forEach(metric => {
  if (typeof metric.value === 'number') {
    metric.status = calculateStatus(metric.value, metric.baselineValue, metric.challengeValue);
  } else {
    metric.status = MetricStatus.NO_DATA;
  }
});

// 统计各状态的数量
const statusStats = computed(() => {
  const stats = {
    [MetricStatus.CHALLENGE_ACHIEVED]: 0,
    [MetricStatus.BASELINE_ACHIEVED]: 0,
    [MetricStatus.CHALLENGE_MISSED]: 0,
    [MetricStatus.BASELINE_MISSED]: 0,
    [MetricStatus.NO_DATA]: 0
  };

  metrics.value.forEach(metric => {
    stats[metric.status] += 1;
  });

  return stats;
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    :full-content="false"
    theme="blue"
  >
    <!-- 指标展示区域 - 3x2 网格布局 -->
    <div class="grid grid-cols-3 grid-rows-2 h-full gap-12px">
      <div
        v-for="metric in metrics"
        :key="metric.name"
        class="relative flex flex-col cursor-pointer border rounded-8px p-12px transition-all duration-300 hover:border-opacity-80 hover:shadow-blue-500/20 hover:shadow-lg"
        :style="{
          borderColor: `${statusConfig[metric.status].color}40`,
          backgroundColor: `${statusConfig[metric.status].color}15`
        }"
      >
        <!-- 指标头部 -->
        <div class="mb-8px flex items-center">
          <div class="flex items-center gap-6px">
            <!-- 状态指示器 -->
            <div
              class="h-6px w-6px animate-pulse rounded-full"
              :style="{ backgroundColor: statusConfig[metric.status].color }"
            />
            <span class="truncate text-12px text-white/80 font-500">{{ metric.name }}</span>
          </div>
        </div>

        <!-- 指标值 -->
        <div class="flex flex-col flex-1 justify-center">
          <div class="text-center">
            <div class="flex items-baseline justify-center gap-2px">
              <span class="text-18px font-700" :style="{ color: statusConfig[metric.status].color }">
                {{ metric.value }}
              </span>
              <span class="text-10px text-white/50">{{ metric.unit }}</span>
            </div>
          </div>
        </div>

        <!-- 状态标识 -->
        <div class="absolute right-2px top-2px text-8px opacity-60">
          {{ statusConfig[metric.status].icon }}
        </div>
      </div>
    </div>

    <!-- 全屏内容插槽 -->
    <template #fullscreen-content>
      <div class="h-full w-full p-24px">
        <!-- 全屏标题和统计 -->
        <div class="mb-24px">
          <!-- 状态统计概览 -->
          <div class="grid grid-cols-5 mb-20px gap-16px">
            <div
              v-for="(config, status) in statusConfig"
              :key="status"
              class="flex flex-col items-center border rounded-8px p-12px"
              :style="{
                borderColor: `${config.color}40`,
                backgroundColor: `${config.color}10`
              }"
            >
              <div class="mb-4px text-16px">{{ config.icon }}</div>
              <div class="mb-2px text-12px text-white/70">{{ config.label }}</div>
              <div class="text-18px font-600" :style="{ color: config.color }">
                {{ statusStats[status as MetricStatus] }}
              </div>
            </div>
          </div>
        </div>

        <!-- 详细指标列表 -->
        <div class="grid grid-cols-2 gap-20px">
          <div
            v-for="metric in metrics"
            :key="metric.id"
            class="border rounded-12px p-20px"
            :style="{
              borderColor: `${statusConfig[metric.status].color}40`,
              backgroundColor: `${statusConfig[metric.status].color}08`
            }"
          >
            <!-- 指标头部信息 -->
            <div class="mb-16px flex items-center justify-between">
              <div class="flex items-center gap-8px">
                <div class="h-8px w-8px rounded-full" :style="{ backgroundColor: statusConfig[metric.status].color }" />
                <h3 class="text-16px text-white font-500">{{ metric.name }}</h3>
                <span class="text-12px">{{ statusConfig[metric.status].icon }}</span>
              </div>
              <div
                class="rounded-4px px-8px py-2px text-10px"
                :style="{
                  backgroundColor: `${statusConfig[metric.status].color}20`,
                  color: statusConfig[metric.status].color
                }"
              >
                {{ statusConfig[metric.status].label }}
              </div>
            </div>

            <!-- 指标值展示 -->
            <div class="mb-16px">
              <div class="flex items-baseline gap-4px">
                <span class="text-32px font-700" :style="{ color: statusConfig[metric.status].color }">
                  {{ metric.value }}
                </span>
                <span class="text-14px text-white/50">{{ metric.unit }}</span>
              </div>
            </div>

            <!-- 目标值对比 -->
            <div class="mb-16px space-y-8px">
              <div class="flex justify-between text-12px">
                <span class="text-white/60">挑战值:</span>
                <span class="text-green-400">{{ metric.challengeValue }}{{ metric.unit }}</span>
              </div>
              <div class="flex justify-between text-12px">
                <span class="text-white/60">基准值:</span>
                <span class="text-blue-400">{{ metric.baselineValue }}{{ metric.unit }}</span>
              </div>
              <div class="flex justify-between text-12px">
                <span class="text-white/60">当前值:</span>
                <span :style="{ color: statusConfig[metric.status].color }">{{ metric.value }}{{ metric.unit }}</span>
              </div>
            </div>

            <!-- 指标描述 -->
            <div class="mb-12px">
              <p class="text-12px text-white/60 leading-relaxed">{{ metric.description }}</p>
            </div>

            <!-- 更新时间 -->
            <div class="text-10px text-white/40">最后更新: {{ metric.lastUpdate }}</div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
