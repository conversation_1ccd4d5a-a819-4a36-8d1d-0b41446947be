<script setup lang="ts">
import { computed } from 'vue';
import { NButton, NIcon, NModal } from 'naive-ui';

defineOptions({
  name: 'DashboardDialog'
});

interface Props {
  /** 对话框显示状态 */
  visible: boolean;
  /** 对话框标题 */
  title: string;
  /** 标题图标 */
  titleIcon?: string;
  /** 对话框宽度 */
  width?: string | number;
  /** 对话框高度 */
  height?: string | number;
  /** 是否显示头部 */
  showHeader?: boolean;
  /** 是否显示页脚 */
  showFooter?: boolean;
  /** 是否显示关闭按钮 */
  showClose?: boolean;
  /** 是否可以点击遮罩关闭 */
  maskClosable?: boolean;
  /** 确认按钮文本 */
  confirmText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 是否显示确认按钮 */
  showConfirm?: boolean;
  /** 是否显示取消按钮 */
  showCancel?: boolean;
  /** 确认按钮加载状态 */
  confirmLoading?: boolean;
  /** 对话框类型 */
  type?: 'default' | 'info' | 'success' | 'warning' | 'error';
  /** 是否全屏内容模式（无padding） */
  fullContent?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
  (e: 'cancel'): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  titleIcon: 'mdi:information-outline',
  width: '480px',
  height: 'auto',
  showHeader: true,
  showFooter: true,
  showClose: true,
  maskClosable: true,
  confirmText: '确认',
  cancelText: '取消',
  showConfirm: true,
  showCancel: true,
  confirmLoading: false,
  type: 'default',
  fullContent: false
});

const emit = defineEmits<Emits>();

// 计算对话框样式
const dialogStyle = computed(() => {
  const style: Record<string, string> = {};

  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width;
  }

  if (props.height && props.height !== 'auto') {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height;
  }

  return style;
});

// 根据类型获取图标颜色
const iconColorClass = computed(() => {
  const colorMap = {
    default: 'text-blue-300',
    info: 'text-blue-300',
    success: 'text-green-300',
    warning: 'text-yellow-300',
    error: 'text-red-300'
  };
  return colorMap[props.type];
});

// 根据类型获取边框颜色
const borderColorClass = computed(() => {
  const colorMap = {
    default: 'border-blue-400/40 bg-blue-500/20',
    info: 'border-blue-400/40 bg-blue-500/20',
    success: 'border-green-400/40 bg-green-500/20',
    warning: 'border-yellow-400/40 bg-yellow-500/20',
    error: 'border-red-400/40 bg-red-500/20'
  };
  return colorMap[props.type];
});

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 处理确认
const handleConfirm = () => {
  emit('confirm');
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
  handleClose();
};

// 处理模态框更新
const handleModalUpdate = (value: boolean) => {
  if (!value) {
    handleClose();
  }
};
</script>

<template>
  <NModal
    :show="visible"
    class="flex items-center justify-center"
    :mask-closable="maskClosable"
    @update:show="handleModalUpdate"
  >
    <div
      class="max-h-[calc(100vh-32px)] max-w-[calc(100vw-32px)] flex flex-col overflow-hidden border border-blue-400/30 rounded-16px bg-gray-900/95 shadow-[0_20px_60px_rgba(0,0,0,0.5)] backdrop-blur-20px"
      :style="dialogStyle"
      role="dialog"
      aria-modal="true"
    >
      <!-- 自定义头部 -->
      <div
        v-if="showHeader"
        class="w-full flex items-center justify-between border-b border-blue-400/20 from-blue-600/20 to-blue-500/10 bg-gradient-to-r p-20px"
      >
        <!-- 左侧标题区域 -->
        <div class="flex items-center gap-12px">
          <div class="h-32px w-32px flex items-center justify-center border rounded-8px" :class="borderColorClass">
            <NIcon size="18" :class="iconColorClass">
              <SvgIcon :icon="titleIcon" />
            </NIcon>
          </div>
          <h2 class="text-20px text-white font-600">{{ title }}</h2>
        </div>

        <!-- 右侧关闭按钮 -->
        <NButton
          v-if="showClose"
          quaternary
          circle
          class="text-gray-400 transition-all duration-200 hover:bg-gray-700/50 hover:text-white"
          @click="handleClose"
        >
          <template #icon>
            <NIcon size="18">
              <SvgIcon icon="mdi:close" />
            </NIcon>
          </template>
        </NButton>
      </div>

      <!-- 对话框内容 -->
      <div
        :class="
          fullContent
            ? 'flex-1 overflow-auto w-full h-full'
            : 'flex-1 overflow-auto  m-10px dashboard-scrollbar w-full h-full'
        "
      >
        <slot />
      </div>

      <!-- 操作按钮 -->
      <div v-if="showFooter" class="border-t border-none p-16px">
        <div class="flex justify-end gap-12px">
          <slot name="footer">
            <NButton
              v-if="showCancel"
              class="px-20px text-gray-400 transition-all duration-200 hover:bg-gray-700/50 hover:text-white"
              @click="handleCancel"
            >
              {{ cancelText }}
            </NButton>

            <NButton
              v-if="showConfirm"
              type="primary"
              class="bg-blue-600 px-24px text-white font-500 transition-all duration-200 hover:bg-blue-500"
              :loading="confirmLoading"
              @click="handleConfirm"
            >
              <template #icon>
                <NIcon size="14">
                  <SvgIcon icon="mdi:check" />
                </NIcon>
              </template>
              {{ confirmText }}
            </NButton>
          </slot>
        </div>
      </div>
    </div>
  </NModal>
</template>
